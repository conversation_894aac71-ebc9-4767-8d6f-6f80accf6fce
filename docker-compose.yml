services:
  ikt-homepage:
    container_name: ikt-homepage
    platform: linux/amd64
    build:
      args:
        port: 3001
      context: ./
      dockerfile: ./docker.d/Dockerfile
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - VITE_CJS_IGNORE_WARNING=true
    networks:
      - app-network
networks:
  app-network:
    name: app-network
    driver: bridge
    external: true
