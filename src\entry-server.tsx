import { renderToString } from "react-dom/server";
import "./index.css";
import {
  createStatic<PERSON><PERSON><PERSON>,
  createStat<PERSON><PERSON><PERSON><PERSON>,
  StaticRouterProvider,
} from "react-router";
import { defaultRouter } from "./router";
import "./index.css";
import { ThemeProvider } from "./contexts/theme-provider.context";
import { ProfileContextProvider } from "./contexts/profile.context";

// Create the handler
const handler = createStaticHandler(defaultRouter);

export async function render(path: string) {
  const request = new Request(`http://localhost${path}`);
  const context = await handler.query(request);

  // ⛔️ Jika context adalah Response, kembalikan langsung (misal redirect / 404)
  if (context instanceof Response) {
    return { errorResponse: context };
  }

  // ✅ Buat router dari context valid
  const router = createStaticRouter(handler.dataRoutes, context);

  const html = renderToString(
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      <ProfileContextProvider>
        <StaticRouterProvider router={router} context={context} />
      </ProfileContextProvider>
    </ThemeProvider>
  );
  return { html };
}
