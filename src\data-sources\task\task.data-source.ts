import { TAS<PERSON> } from "@/constants/api";
import { networkWithAuth } from "@/lib/network";
import TaskModel from "@/models/task.model";
import { ResponseAPI } from "@/types/response-api.type";

export async function getTaskLists(statusId: number): Promise<TaskModel[]> {
  try {
    const params: any = {
      status: statusId,
    };

    const { data } = await networkWithAuth().get(TASK, {
      params: params,
    });
    const response = data.data;

    return response as TaskModel[];
  } catch (_) {
    return [];
  }
}

export async function createTask({
  title,
  description,
  assignee_id,
}: {
  title: string;
  description: string;
  assignee_id: number;
}): Promise<ResponseAPI<undefined>> {
  try {
    const payload = {
      title: title,
      description: description,
      assignee_id: assignee_id,
    };

    const { data } = await networkWithAuth().post(TASK, payload);

    return {
      success: true,
      message: data.message,
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message ?? "<PERSON><PERSON><PERSON><PERSON>",
    };
  }
}

export async function updateStatusTask({
  id,
  status_id,
}: {
  id: number;
  status_id: number;
}): Promise<ResponseAPI<undefined>> {
  try {
    const payload = {
      status_id: status_id,
    };

    const { data } = await networkWithAuth().patch(`${TASK}/${id}/status`, payload);

    return {
      success: true,
      message: data.message,
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message ?? "Terjadi Kesalahan",
    };
  }
}

export async function updateTask({
    id,
  title,
  description,
}: {
    id: number
  title: string;
  description: string;
}): Promise<ResponseAPI<undefined>> {
  try {
    const payload = {
      title: title,
      description: description
    };

    const { data } = await networkWithAuth().put(`${TASK}/${id}`, payload);

    return {
      success: true,
      message: data.message,
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message ?? "Terjadi Kesalahan",
    };
  }
}

export async function updateReportTask({
    id,
  report
}: {
    id: number
  report: string;
}): Promise<ResponseAPI<undefined>> {
  try {
    const payload = {
      report: report,
    };

    const { data } = await networkWithAuth().patch(`${TASK}/${id}/report`, payload);

    return {
      success: true,
      message: data.message,
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message ?? "Terjadi Kesalahan",
    };
  }
}