import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { updateStatusTask } from "@/data-sources/task/task.data-source";
import TaskModel from "@/models/task.model";
import { useMemo, useState } from "react";
import { toast } from "sonner";

interface IProps {
  data: TaskModel;
  toStatus: number
  onClose: () => void;
  onSuccess: () => void;
}

export default function UpdateStatusTaskDialog({
  data,
  toStatus,
  onClose,
  onSuccess,
}: IProps) {
  const [loading, setLoading] = useState<boolean>(false);

  const handleUpdate = async (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ) => {
    e.preventDefault();
    setLoading(true);
    const { success, message } = await updateStatusTask({ id: data.id, status_id: toStatus });
    setLoading(false);

    if (success) {
      onSuccess();
      toast.success(message);
    } else {
      toast.error(message);
    }
  };

  const description = useMemo(() => {
    switch (toStatus) {
        case 1:
            return `Are you sure want to update this task to To Do?`;
        case 2:
            return `Are you sure want to update this task to Doing?`;
        case 3:
            return `Are you sure want to update this task to Done?`;
        case 4:
            return `Are you sure want to update this task to Canceled?`;
    }
    
    return `Are you sure want to update this task to ${toStatus}?`;
  }, [toStatus]);

  return (
    <AlertDialog open onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Update Status
          </AlertDialogTitle>
          <AlertDialogDescription>
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button onClick={handleUpdate} loading={loading}>
              Continue
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
