import { SecureStorageEnum } from "@/enums/secure-storage.enum";
import axios, { type AxiosRequestConfig } from "axios";
import secureLocalStorage from "react-secure-storage";

const axiosConfig: AxiosRequestConfig = {
  baseURL: "/api"
};

const network = axios.create(axiosConfig);

export const networkWithAuth = () => {
  const authNetwork = network;
  const token = secureLocalStorage.getItem(SecureStorageEnum.token) as
    | string
    | null;

  if (token != null)
    authNetwork.defaults.headers.Authorization = `Bearer ${token}`;

  authNetwork.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response.status == 401 && token != undefined)
        secureLocalStorage.removeItem(SecureStorageEnum.token);

      return Promise.reject(error);
    }
  );

  return authNetwork;
};

export default network;
