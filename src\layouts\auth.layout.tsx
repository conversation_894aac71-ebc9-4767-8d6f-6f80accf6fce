import { useProfileContext } from "@/contexts/profile.context";
import { useEffect } from "react";
import { Outlet, useNavigate } from "react-router";

const AuthLayout = () => {
  const { user, state } = useProfileContext();
  const navigate = useNavigate();

  useEffect(() => {
    if (state == "AUTHENTICATED" && user != undefined) {
      navigate("/", { replace: true });
    }
  }, [user, state]);

  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm md:max-w-3xl">
        <Outlet />
      </div>
    </div>
  );
};

export default AuthLayout;
