{"name": "homepage", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode test", "build": "npm run build:client -- --mode staging && npm run build:server -- --mode staging", "build:client": "tsc -b && vite build --ssrManifest --outDir dist/client", "build:server": "tsc -b && vite build --ssr src/entry-server.tsx --outDir dist/server", "serve": "cross-env NODE_ENV=staging PORT=3001 node server.js", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@tailwindcss/vite": "^4.1.13", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.8.1", "dotenv": "^17.2.2", "express": "^5.1.0", "formik": "^2.4.6", "http-proxy-middleware": "^3.0.5", "lucide-react": "^0.542.0", "next-themes": "^0.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router": "^7.8.2", "react-secure-storage": "^1.3.2", "sirv": "^3.0.2", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.13", "yup": "^1.7.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/node": "^24.3.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react-swc": "^4.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "tw-animate-css": "^1.3.8", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}