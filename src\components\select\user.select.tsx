import { FC, useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import UserModel from "@/models/user.model";
import { getSelectUserLists } from "@/data-sources/user/user.data-source";

interface IProps {
  value?: string;
  onChange: (value?: UserModel) => void;
}

const UserSelect: FC<IProps> = ({ value, onChange }) => {
  const [data, setData] = useState<UserModel[]>([]);

  useEffect(() => {
    getSelectUserLists().then((data) => {
      setData(data);
    });
  }, []);

  return (
    <Select
      value={value}
      onValueChange={(value) => {
        if (value == "0" || value == undefined) {
          onChange(undefined);
          return;
        }

        const selected = data.find((item) => item.id == Number(value));
        onChange(selected!);
      }}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select Element" />
      </SelectTrigger>
      <SelectContent>
        {data.map((item) => (
          <SelectItem key={item.id} value={item.id.toString()}>
            {item.name} <small className="text-muted-foreground">({item.role?.name})</small>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default UserSelect;
