import fs from "node:fs/promises"; // NodeJS async file system module, 'interact' static files
import express from "express"; // Express is NodeJS library for building api
import dotenv from "dotenv";
import { createProxyMiddleware } from "http-proxy-middleware";

/**
  This file is used to set up a NodeJS Express server to handle SSR for our React application. It dynamically selects the appropriate SSR render function and template based on the environment (development or production) and serves the rendered HTML to clients upon request.

  The server is set up to serve the client-side assets in production and use Vite's middleware in development. The server also reads the SSR manifest file in production to determine the appropriate render function to use.
 */

// Constants
const isTest = process.env.NODE_ENV === "test";
const mode = process.env.NODE_ENV;

dotenv.config({ path: `./.env.staging` });

const port = process.env.PORT || 4000;
const base = process.env.BASE || "/";

// Cached production assets
const templateHtml = !isTest
  ? await fs.readFile("./dist/client/index.html", "utf-8")
  : "";
const ssrManifest = !isTest
  ? await fs.readFile("./dist/client/.vite/ssr-manifest.json", "utf-8")
  : undefined;

// Create http server
const app = express();

// Add Vite or respective production middlewares
let vite;
if (isTest) {
  const { createServer } = await import("vite");
  vite = await createServer({
    server: {
      middlewareMode: true,
      proxy: {
        "/api": {
          target: `${process.env.VITE_BE_SCHEME}://${process.env.VITE_BE_ORIGIN}/api/admin`,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ""),
        },
      },
    },
    appType: "custom",
    base,
  });
  app.use(vite.middlewares);
} else {
  const compression = (await import("compression")).default;
  const sirv = (await import("sirv")).default;

  // ⬇️ Tambah proxy manual ke backend Laravel API
  app.use(
    "/api",
    createProxyMiddleware({
      target: `${process.env.VITE_BE_SCHEME}://${process.env.VITE_BE_ORIGIN}/api`,
      changeOrigin: true,
    })
  );

  app.use(compression());
  app.use(base, sirv("./dist/client", { extensions: [] }));
}

// Serve HTML
app.use("*all", async (req, res) => {
  try {
    const url = `/${req.originalUrl.replace(base, "")}`;

    let template;
    let render;
    if (isTest) {
      // Always read fresh template in development
      template = await fs.readFile("./index.html", "utf-8");
      template = await vite.transformIndexHtml(url, template);
      render = (await vite.ssrLoadModule("/src/entry-server.tsx")).render;
    } else {
      template = templateHtml;
      render = (await import("./dist/server/entry-server.js")).render;
    }

    const rendered = await render(url, ssrManifest);

    const html = template
      .replace(`<!--app-head-->`, rendered.head ?? "")
      .replace(`<!--app-html-->`, rendered.html ?? "");

    res.status(200).set({ "Content-Type": "text/html" }).send(html);
  } catch (e) {
    vite?.ssrFixStacktrace(e);
    console.log(e.stack);
    res.status(500).end(e.stack);
  }
});

// Start http server — bind to all interfaces so container is reachable from host/network
app.listen(port, "0.0.0.0", () => {
  console.log(`Server started and listening on 0.0.0.0:${port}`);
});
