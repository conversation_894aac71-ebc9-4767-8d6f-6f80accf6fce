import PaginationType, {
  Link as PaginationLinkType,
} from "@/types/pagination.type";
import { FC, useEffect, useState } from "react";
import {
  Pagination,
  PaginationLink,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
} from "../ui/pagination";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Button } from "../ui/button";
import { useLocation, useNavigate, useSearchParams } from "react-router";

const pageSizeLists: number[] = [10, 20, 50, 100];

interface IProps {
  data: PaginationType;
}

const PaginationDatatableComponent: FC<IProps> = ({ data }) => {
  const [links, setLinks] = useState<PaginationLinkType[]>([]);
  const [searchParams] = useSearchParams(); // Hook untuk mengelola query params
  const location = useLocation(); // Mendapatkan objek lokasi saat ini (termasuk pathname)
  const navigate = useNavigate(); // Hook untuk navigasi programatik
  const currentPage = Number(searchParams.get("page") ?? "1");
  const show = Number(searchParams.get("per_page") ?? "10");

  const handleNavigation = (
    paramsUpdater: (params: URLSearchParams) => void
  ) => {
    // Buat objek URLSearchParams baru dari yang sudah ada
    // Ini penting karena searchParams dari useSearchParams tidak langsung mutable
    const newParams = new URLSearchParams(searchParams.toString());

    paramsUpdater(newParams); // Panggil updater untuk memodifikasi params

    // Gunakan navigate dengan object sebagai argumen untuk memperbarui URL
    navigate(`${location.pathname}?${newParams.toString()}`);
  };

  useEffect(() => {
    setLinks(
      data.links.filter(
        (item) => !["&laquo; Previous", "Next &raquo;"].includes(item.label)
      )
    );
  }, [data]);

  return (
    <div className="flex items-center justify-end space-x-2">
      <div className="flex-1 text-sm text-muted-foreground">
        Page {currentPage} of {data.last_page.toLocaleString()}
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() =>
            handleNavigation((params) => {
              params.set("page", (currentPage - 1).toString());
            })
          }
          disabled={currentPage === 1}
        >
          Previous
        </Button>
        <Pagination>
          <PaginationContent>
            {links.map((link, key) => (
              <PaginationItem key={`links-${link.label}-${key}`}>
                {/^\d+$/.test(link.label) && ( // Memastikan label adalah angka
                  <PaginationLink
                    size="sm"
                    className="cursor-pointer"
                    isActive={link.active}
                    onClick={
                      link.active
                        ? undefined // Nonaktifkan klik jika sudah aktif
                        : () =>
                            handleNavigation((params) => {
                              params.set("page", link.label);
                            })
                    }
                  >
                    {link.label}
                  </PaginationLink>
                )}
                {link.label === "..." && <PaginationEllipsis />}
              </PaginationItem>
            ))}
          </PaginationContent>
        </Pagination>
        <Button
          variant="outline"
          size="sm"
          onClick={() =>
            handleNavigation((params) => {
              params.set("page", (currentPage + 1).toString());
            })
          }
          disabled={currentPage >= data.last_page}
        >
          Next
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Show: {show}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {pageSizeLists.map((size) => (
              <DropdownMenuItem
                disabled={show === size}
                key={`size-${size}`}
                onClick={() =>
                  handleNavigation((params) => {
                    params.set("per_page", size.toString());
                    params.set("page", "1"); // Kembali ke halaman 1 saat mengubah per_page
                  })
                }
              >
                {size}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default PaginationDatatableComponent;
