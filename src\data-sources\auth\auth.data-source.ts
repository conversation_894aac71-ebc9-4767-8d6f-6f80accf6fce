import { AUTH_LOGIN, AUTH_LOGOUT } from "@/constants/api";
import { SecureStorageEnum } from "@/enums/secure-storage.enum";
import network, { networkWithAuth } from "@/lib/network";
import { ResponseAPI } from "@/types/response-api.type";
import secureLocalStorage from "react-secure-storage";

export async function doLogin(credentials: {
  email: string;
  password: string;
}): Promise<ResponseAPI<string | undefined>> {
  try {
    const { data } = await network.post(AUTH_LOGIN, credentials);
    const response = data.data;

    secureLocalStorage.setItem(SecureStorageEnum.token, response as string);

    return {
      success: true,
      message: data.message,
      data: response as string,
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message ?? "<PERSON><PERSON><PERSON><PERSON>",
      data: error.response?.data?.status_code,
    };
  }
}

export async function doLogout(): Promise<ResponseAPI<void>> {
  try {
    const { data } = await networkWithAuth().post(AUTH_LOGOUT);

    return {
      success: true,
      message: data.message,
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message ?? "Ter<PERSON><PERSON><PERSON><PERSON>",
    };
  }
}
