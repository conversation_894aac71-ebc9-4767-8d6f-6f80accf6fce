import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { doLogin } from "@/data-sources/auth/auth.data-source";
import { useState } from "react";
import { useFormik } from "formik";
import * as yup from "yup";
import { toast } from "sonner";
import { useProfileContext } from "@/contexts/profile.context";

interface formValues {
  email: string;
  password: string;
}

const initialValues: formValues = {
  email: "",
  password: "",
};

export default function LoginPage() {
  const [loading, setLoading] = useState<boolean>(false);
  const { getProfile } = useProfileContext();

  const handleLogin = async (values: formValues) => {
    setLoading(true);
    const response = await doLogin({
      email: values.email,
      password: values.password,
    });
    setLoading(false);
    if (response.success) {
      await getProfile();
    } else {
      toast.error(response.message ?? "");
    }
  };

  const formik = useFormik({
    initialValues: initialValues,
    onSubmit: handleLogin,
    validationSchema: yup.object({
      email: yup.string().required("E-mail masih kosong!"),
      password: yup.string().required("Password masih kosong!"),
    }),
  });

  return (
    <div className="flex flex-col gap-6">
      <Card className="overflow-hidden p-0">
        <CardContent className="grid p-0 md:grid-cols-2">
          <form className="p-6 md:p-8" onSubmit={formik.handleSubmit}>
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <h1 className="text-2xl font-bold">Welcome back</h1>
                <p className="text-muted-foreground text-balance">
                  Login to your Weton Kaya account
                </p>
              </div>
              <div className="grid gap-3">
                <Label htmlFor="email">E-mail</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="email"
                  required
                  value={formik.values.email}
                  onChange={(e) => {
                    formik.values.email = e.target.value;
                    formik.validateField("email");
                  }}
                />
                <label
                  htmlFor="error"
                  className={`text-[12px] text-red-500 ${
                    formik.touched.email && formik.errors.email
                      ? ""
                      : "hidden"
                  }`}
                >
                  {formik.touched.email && formik.errors.email
                    ? formik.errors.email
                    : undefined}
                </label>
              </div>
              <div className="grid gap-3">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  required
                  value={formik.values.password}
                  onChange={(e) => {
                    formik.values.password = e.target.value;
                    formik.validateField("password");
                  }}
                />
                <label
                  htmlFor="error"
                  className={`text-[12px] text-red-500 ${
                    formik.touched.password && formik.errors.password
                      ? ""
                      : "hidden"
                  }`}
                >
                  {formik.touched.password && formik.errors.password
                    ? formik.errors.password
                    : undefined}
                </label>
              </div>
              <Button type="submit" className="w-full" loading={loading}>
                Login
              </Button>
            </div>
          </form>
          <div className="bg-muted relative hidden md:block">
            <img
              src="https://placehold.co/600x400"
              alt="Image"
              className="absolute inset-0 h-full w-full object-cover"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
