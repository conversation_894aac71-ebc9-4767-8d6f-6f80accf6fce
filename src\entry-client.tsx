import { hydrateRoot } from "react-dom/client";
import { Toaster } from "./components/ui/sonner";
import { createBrowserRouter, RouterProvider } from "react-router";
import "./index.css";
import { defaultRouter } from "./router";
import { ThemeProvider } from "./contexts/theme-provider.context";
import { ProfileContextProvider } from "./contexts/profile.context";

const router = createBrowserRouter(defaultRouter);

hydrateRoot(
  document.getElementById("root") as HTMLElement,
  <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
    <ProfileContextProvider>
      <Toaster richColors position="top-right" closeButton />
      <RouterProvider router={router} />
    </ProfileContextProvider>
  </ThemeProvider>
);
