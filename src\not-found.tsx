import { <PERSON> } from "react-router";
import { <PERSON><PERSON> } from "./components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./components/ui/card";

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-muted">
      <Card className="w-full max-w-md text-center p-6">
        <CardHeader>
          <CardTitle className="text-4xl font-bold text-destructive">
            404
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Halaman tidak ditemukan
          </p>
        </CardHeader>
        <CardContent>
          <p className="mb-6">Sepertinya halaman yang kamu cari tidak ada.</p>
          <Button asChild>
            <Link to="/">Kembali ke Beranda</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
