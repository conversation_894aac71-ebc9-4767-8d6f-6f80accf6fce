import { RouteObject } from "react-router";
import NotFound from "./not-found";
import DashboardLayout from "./layouts/dashboard.layout";
import AuthLayout from "./layouts/auth.layout";
import LoginPage from "./pages/auth/login.page";
import UserPage from "./pages/user/user.page";
import HomePage from "./pages/home/<USER>";

export const defaultRouter: RouteObject[] = [
  {
    path: "/",
    element: <DashboardLayout />,
    children: [
      {
        index: true,
        element: <HomePage/>,
      },
      {
        path: "/user",
        element: <UserPage />,
      },
    ],
  },
  {
    path: "/",
    element: <AuthLayout />,
    children: [
      {
        path: "/login",
        element: <LoginPage/>,
      },
    ],
  },
  {
    path: "*",
    element: <NotFound />,
  },
];
