# Secure minimal production image with proper permission handling
# This version handles all permission issues and Vite cache problems

# --- Build stage ---
FROM node:20-alpine AS builder

# Define build arguments for dynamic build command and environment
# These ARGs will be passed from docker-compose.yml
ARG port

WORKDIR /app

# Copy package files first for better layer caching
# This step is crucial for efficient caching, as dependencies rarely change
COPY package.json yarn.lock ./

# Install all dependencies (including devDependencies for build)
# Use --frozen-lockfile for consistent builds based on yarn.lock
# --silent and --non-interactive reduce output verbosity
# --network-timeout for robustness during downloads
RUN yarn --frozen-lockfile --silent --non-interactive --network-timeout 100000

# Copy source code and configuration files
# Copy all project files into the builder stage
COPY . .

# Build the application using the specified build command
# Set NODE_ENV during build to ensure correct Vite build mode (e.g., 'staging' or 'production')
ENV NODE_ENV="staging"

RUN yarn build

# --- Production dependencies stage ---
FROM node:20-alpine AS deps

WORKDIR /app

# Copy package files
COPY package.json yarn.lock ./

# Install only production dependencies to keep the final image small
# Clean yarn cache and temporary files to further reduce image size
RUN yarn --frozen-lockfile --production --silent --non-interactive --network-timeout 100000 && \
    yarn cache clean && \
    rm -rf /tmp/* /var/cache/apk/* /root/.npm /root/.yarn-cache

# --- Final production stage ---
FROM node:20-alpine AS production

# Define runtime arguments for environment and port
# These ARGs will be passed from docker-compose.yml and used for runtime ENV and healthcheck
ARG port

# Install dumb-init for proper signal handling
# dumb-init ensures that signals (like SIGTERM from Docker) are properly propagated to the Node.js process,
# allowing for graceful shutdowns.
RUN apk add --no-cache dumb-init

# Create a non-root user with specific UID/GID for security best practices
# Using a fixed UID/GID (1001) helps with consistent permissions across environments
RUN addgroup -g 1001 -S nodejs && \
    adduser -S reactuser -u 1001 -G nodejs

# Create app directory and set permissions
# Ensure the /app directory exists and is owned by the non-root user
RUN mkdir -p /app && \
    chown -R reactuser:nodejs /app

WORKDIR /app

# Copy production dependencies with proper ownership from 'deps' stage
COPY --from=deps --chown=reactuser:nodejs /app/node_modules ./node_modules

# Copy built application from 'builder' stage with proper ownership
# This includes the client build (dist/client), server build (dist/server),
# server.js, index.html, package.json, and any .env files (read by server.js at runtime)
COPY --from=builder --chown=reactuser:nodejs /app/dist ./dist
COPY --from=builder --chown=reactuser:nodejs /app/server.js ./
COPY --from=builder --chown=reactuser:nodejs /app/index.html ./
COPY --from=builder --chown=reactuser:nodejs /app/package.json ./
COPY --from=builder --chown=reactuser:nodejs /app/.env.staging ./

# Create necessary directories with proper permissions to prevent runtime errors
# This is specifically for Vite's internal cache/temp files if any are generated at runtime
RUN mkdir -p /app/node_modules/.vite && \
    chown -R reactuser:nodejs /app/node_modules/.vite

# Switch to non-root user for running the application
USER reactuser

# Set environment variables for the runtime container
# NODE_ENV is crucial for server.js to load the correct .env file and behave correctly
ENV NODE_ENV="staging"
# PORT is used by server.js to listen on the correct port
ENV PORT=${port}
# VITE_CJS_IGNORE_WARNING is a specific environment variable for Vite/Node.js compatibility
ENV VITE_CJS_IGNORE_WARNING=true

# Health check to ensure the application is running and responsive
# Uses the dynamic PORT for the health check URL
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:${PORT}', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Expose the port that the application will listen on
EXPOSE ${port}

# Use dumb-init as the entrypoint for robust signal handling
ENTRYPOINT ["dumb-init", "--"]
# Default command to run the Node.js server
CMD ["node", "server.js"]