import PaginationDatatableComponent from "@/components/datatable/pagination-datatable.component";
import HeaderComponent from "@/components/header/header.component";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import PaginationType from "@/types/pagination.type";
import { useEffect, useState } from "react";
import { useSearchParams } from "react-router";
import UserModel from "@/models/user.model";
import { getUserLists } from "@/data-sources/user/user.data-source";
import CreateUserDialog from "./components/create-user.dialog";

export default function UserPage() {
  const [searchParams] = useSearchParams();
  const [data, setData] = useState<PaginationType<UserModel> | undefined>(
    undefined
  );
  const page = Number(searchParams.get("page") ?? "1");
  const perPage = Number(searchParams.get("per_page") ?? "10");

  const getData = async () => {
    const response = await getUserLists({
      page,
      per_page: perPage
    });
    setData(response);
  };

  useEffect(() => {
    getData();
  }, [page, perPage]);

  const handleSuccessCreated = () => {
    getData();
  };

  return (
    <>
      <HeaderComponent
        breadcrumbs={[{ title: "Home", url: "/" }, { title: "User" }]}
      />

      <main className="px-4 grid gap-4">
        <div className="flex items-center">
          <div className="relative w-full flex-1">
            
          </div>
          <div className="ml-auto flex items-center gap-2">
            <CreateUserDialog onSuccess={handleSuccessCreated} />
          </div>
        </div>
        <div className="overflow-y-auto rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className="border-b-[1px]">
                <TableHead className="h-10 px-2 text-left align-middle font-medium text-muted-foreground">
                  No
                </TableHead>
                <TableHead className="h-10 px-2 align-middle font-medium text-muted-foreground text-left">
                  Name
                </TableHead>
                <TableHead className="h-10 px-2 text-left align-middle font-medium text-muted-foreground">
                  E-mail
                </TableHead>
                <TableHead className="h-10 px-2 text-left align-middle font-medium text-muted-foreground">
                  Role
                </TableHead>
              </TableRow>
            </TableHeader>

            <TableBody>
              {(data?.data ?? []).length > 0 ? (
                (data?.data ?? []).map((item, key) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      {((page - 1) * perPage + (key + 1)).toLocaleString()}
                    </TableCell>
                    <TableCell>{item.name}</TableCell>
                    <TableCell>{item.email}</TableCell>
                    <TableCell>{item.role?.name}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {data != undefined && <PaginationDatatableComponent data={data} />}
      </main>
    </>
  );
}
