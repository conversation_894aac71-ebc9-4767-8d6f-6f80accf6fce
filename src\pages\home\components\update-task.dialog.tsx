import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>lose,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { updateTask } from "@/data-sources/task/task.data-source";
import TaskModel from "@/models/task.model";
import { useFormik } from "formik";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import * as yup from "yup";

interface IProps {
  data: TaskModel;
  onClose: () => void;
  onSuccess: () => void;
}

interface formValues {
  title: string;
  description: string;
}

const initialValues: formValues = {
  title: "",
  description: "",
};

export default function UpdateTaskDialog({
  data,
  onClose,
  onSuccess,
}: IProps) {
  const [loading, setLoading] = useState<boolean>(false);

  const handleSubmit = async (values: formValues) => {
    setLoading(true);
    const { success, message } = await updateTask({
      id: data.id,
      title: values.title,
      description: values.description,
    });
    setLoading(false);

    if (success) {
      onSuccess();
      toast.success(message);
    } else {
      toast.error(message);
    }
  };

  const handleCancel = (_: boolean) => {
    if (loading) return;

    onClose();
  };

  const formik = useFormik({
    initialValues: initialValues,
    onSubmit: handleSubmit,
    validationSchema: yup.object({
      title: yup.string().required("Title is required!"),
      description: yup.string().required("Description is required!"),
    }),
  });

  const disabledButton = useMemo(() => {
    return formik.values.title == "" || formik.values.description == "";
  }, [formik]);

  useMemo(() => {
    formik.setValues({ title: data.title, description: data.description });
  }, [data]);

  return (
    <Dialog open onOpenChange={handleCancel}>
      <DialogContent className="sm:max-w-[425px] mx-6 max-h-[80dvh]">
        <form onSubmit={formik.handleSubmit} className="grid gap-4">
          <DialogHeader>
            <DialogTitle>Update Task</DialogTitle>
            <DialogDescription>
              Are you sure want to update this task?
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4">
            <div className="grid gap-3">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                required
                autoComplete="off"
                value={formik.values.title}
                onChange={(e) => {
                  formik.values.title = e.target.value;
                  formik.validateField("title");
                }}
              />
              <label
                htmlFor="error"
                className={`text-[12px] text-red-500 ${
                  formik.errors.title ? "" : "hidden"
                }`}
              >
                {formik.errors.title ? formik.errors.title : undefined}
              </label>
            </div>

            <div className="grid gap-3">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                required
                rows={5}
                autoComplete="off"
                value={formik.values.description}
                onChange={(e) => {
                  formik.values.description = e.target.value;
                  formik.validateField("description");
                }}
              />
              <label
                htmlFor="error"
                className={`text-[12px] text-red-500 ${
                  formik.errors.description ? "" : "hidden"
                }`}
              >
                {formik.errors.description ? formik.errors.description : undefined}
              </label>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button type="submit" disabled={disabledButton} loading={loading}>
              Save changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
