import { getTask<PERSON><PERSON> } from "@/data-sources/task/task.data-source";
import TaskModel from "@/models/task.model";
import UpdateReportTaskDialog from "@/pages/home/<USER>/update-report.task.dialog";
import UpdateStatusTaskDialog from "@/pages/home/<USER>/update-status-task.dialog";
import UpdateTaskDialog from "@/pages/home/<USER>/update-task.dialog";
import { createContext, ReactNode, useContext, useState } from "react";

interface IUpdateStatus {
  data: TaskModel;
  toStatus: number;
}

export type TaskContextState = {
  todoData: TaskModel[];
  doingData: TaskModel[];
  doneData: TaskModel[];
  canceledData: TaskModel[];
  updateStatus: (data: TaskModel, toStatus: number) => void;
  getTodoLists: () => Promise<void>;
  getDoingLists: () => Promise<void>;
  getDoneLists: () => Promise<void>;
  getCanceledLists: () => Promise<void>;
  updateTask: (data: TaskModel) => void;
  updateReport: (data: TaskModel) => void;
};
const initialValues: TaskContextState = {
  todoData: [],
  doingData: [],
  doneData: [],
  canceledData: [],
  updateStatus: () => {},
  getTodoLists: async () => {},
  getDoingLists: async () => {},
  getDoneLists: async () => {},
  getCanceledLists: async () => {},
  updateTask: () => {},
  updateReport: () => {},
};
const TaskContext = createContext<TaskContextState>(initialValues);

export const useTaskContext = () => useContext(TaskContext);

export const TaskContextProvider = ({ children }: { children: ReactNode }) => {
  const [todoData, setTodoData] = useState<TaskModel[]>([]);
  const [doingData, setDoingData] = useState<TaskModel[]>([]);
  const [doneData, setDoneData] = useState<TaskModel[]>([]);
  const [canceledData, setCanceledData] = useState<TaskModel[]>([]);
  const [updateStatus, setUpdateStatus] = useState<IUpdateStatus | undefined>(
    undefined
  );
  const [updateTask, setUpdateTask] = useState<TaskModel | undefined>(
    undefined
  );
  const [updateReport, setUpdateReport] = useState<TaskModel | undefined>(
    undefined
  );

  const getTodoLists = async () => {
    const response = await getTaskLists(1);
    setTodoData(response);
  };

  const getDoingLists = async () => {
    const response = await getTaskLists(2);
    setDoingData(response);
  };

  const getDoneLists = async () => {
    const response = await getTaskLists(3);
    setDoneData(response);
  };

  const getCanceledLists = async () => {
    const response = await getTaskLists(4);
    setCanceledData(response);
  };

  const handleUpdateStatus = async (data: TaskModel, toStatus: number) => {
    setUpdateStatus({ data, toStatus });
  };

  const handleUpdateTask = async (data: TaskModel) => {
    setUpdateTask(data);
  };

  const handleUpdateReport = async (data: TaskModel) => {
    setUpdateReport(data);
  };

  const handleUpdatedStatus = (status: number, prevStatus: number) => {
    if (status == 1) {
      getTodoLists();
    } else if (status == 2) {
      getDoingLists();
    } else if (status == 3) {
      getDoneLists();
    } else if (status == 4) {
      getCanceledLists();
    }

    loadByStatus(prevStatus);
    setUpdateStatus(undefined);
  };

  const handleUpdatedTask = (status: number) => {
    loadByStatus(status);
    setUpdateTask(undefined);
  };

  const handleUpdatedReport = (status: number) => {
    loadByStatus(status);
    setUpdateReport(undefined);
  };

  const loadByStatus = (status: number) => {
    if (status == 1) {
      getTodoLists();
    } else if (status == 2) {
      getDoingLists();
    } else if (status == 3) {
      getDoneLists();
    } else if (status == 4) {
      getCanceledLists();
    }
  };

  return (
    <TaskContext.Provider
      value={{
        todoData,
        doingData,
        doneData,
        canceledData,
        updateStatus: handleUpdateStatus,
        getTodoLists,
        getDoingLists,
        getDoneLists,
        getCanceledLists,
        updateTask: handleUpdateTask,
        updateReport: handleUpdateReport,
      }}
    >
      {children}

      {updateStatus && (
        <UpdateStatusTaskDialog
          data={updateStatus.data}
          toStatus={updateStatus.toStatus}
          onClose={() => setUpdateStatus(undefined)}
          onSuccess={() =>
            handleUpdatedStatus(
              updateStatus.toStatus,
              updateStatus.data.status_id
            )
          }
        />
      )}
      {updateTask && (
        <UpdateTaskDialog
          data={updateTask}
          onClose={() => setUpdateTask(undefined)}
          onSuccess={() => handleUpdatedTask(updateTask.status_id)}
        />
      )}
      {updateReport && (
        <UpdateReportTaskDialog
          data={updateReport}
          onClose={() => setUpdateReport(undefined)}
          onSuccess={() => handleUpdatedReport(updateReport.status_id)}
        />
      )}
    </TaskContext.Provider>
  );
};
