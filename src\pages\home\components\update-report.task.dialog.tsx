import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>lose,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { updateReportTask } from "@/data-sources/task/task.data-source";
import TaskModel from "@/models/task.model";
import { useFormik } from "formik";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import * as yup from "yup";

interface IProps {
  data: TaskModel;
  onClose: () => void;
  onSuccess: () => void;
}

interface formValues {
  report: string;
}

const initialValues: formValues = {
  report: "",
};

export default function UpdateReportDialog({
  data,
  onClose,
  onSuccess,
}: IProps) {
  const [loading, setLoading] = useState<boolean>(false);

  const handleSubmit = async (values: formValues) => {
    setLoading(true);
    const { success, message } = await updateReportTask({
      id: data.id,
      report: values.report,
    });
    setLoading(false);

    if (success) {
      onSuccess();
      toast.success(message);
    } else {
      toast.error(message);
    }
  };

  const handleCancel = (_: boolean) => {
    if (loading) return;

    onClose();
  };

  const formik = useFormik({
    initialValues: initialValues,
    onSubmit: handleSubmit,
    validationSchema: yup.object({
      report: yup.string().required("Report is required!"),
    }),
  });

  const disabledButton = useMemo(() => {
    return formik.values.report == "";
  }, [formik]);

  useMemo(() => {
    formik.setValues({ report: data.report ?? "" });
  }, [data]);

  return (
    <Dialog open onOpenChange={handleCancel}>
      <DialogContent className="sm:max-w-[425px] mx-6 max-h-[80dvh]">
        <form onSubmit={formik.handleSubmit} className="grid gap-4">
          <DialogHeader>
            <DialogTitle>Update Report</DialogTitle>
            <DialogDescription>
              Are you sure want to update this report?
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4">
            <div className="grid gap-3">
              <Label htmlFor="report">Report</Label>
              <Textarea
                id="report"
                name="report"
                rows={5}
                required
                autoComplete="off"
                value={formik.values.report}
                onChange={(e) => {
                  formik.values.report = e.target.value;
                  formik.validateField("report");
                }}
              />
              <label
                htmlFor="error"
                className={`text-[12px] text-red-500 ${
                  formik.errors.report ? "" : "hidden"
                }`}
              >
                {formik.errors.report ? formik.errors.report : undefined}
              </label>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button type="submit" disabled={disabledButton} loading={loading}>
              Save changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
