import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardH<PERSON>er,
  Card<PERSON>itle,
} from "@/components/ui/card";
import TaskModel from "@/models/task.model";
import { useEffect, useState } from "react";
import CreateTaskDialog from "./create-task.dialog";
import { Button } from "@/components/ui/button";
import { useTaskContext } from "@/contexts/task.context";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreVertical } from "lucide-react";
import { useProfileContext } from "@/contexts/profile.context";

interface IProps {
  title: string;
  statusId: number;
}

export default function CardTaskComponent({ title, statusId }: IProps) {
  const { user } = useProfileContext();
  const {
    updateStatus,
    updateTask,
    updateReport,
    getTodoLists,
    getDoingLists,
    getDoneLists,
    getCanceledLists,
    todoData,
    doingData,
    doneData,
    canceledData,
  } = useTaskContext();
  const [data, setData] = useState<TaskModel[]>([]);

  const getData = async () => {
    if (statusId == 1) {
      getTodoLists();
    } else if (statusId == 2) {
      getDoingLists();
    } else if (statusId == 3) {
      getDoneLists();
    } else if (statusId == 4) {
      getCanceledLists();
    }
  };

  useEffect(() => {
    getData();
  }, [statusId]);

  useEffect(() => {
    if (statusId == 1) {
      setData(todoData);
    } else if (statusId == 2) {
      setData(doingData);
    } else if (statusId == 3) {
      setData(doneData);
    } else if (statusId == 4) {
      setData(canceledData);
    }
  }, [statusId, todoData, doingData, doneData, canceledData]);

  return (
    <>
      <Card className="flex-none w-80 h-min">
        <CardHeader>
          <CardTitle>{title}</CardTitle>

          {statusId == 1 && (
            <CardAction>
              <CreateTaskDialog onSuccess={() => getData()} />
            </CardAction>
          )}
        </CardHeader>

        <CardContent className="max-h-[768px] overflow-y-auto grid gap-4">
          {data.length > 0 ? (
            data.map((item) => (
              <Card key={item.id}>
                <CardHeader>
                  <CardTitle className="text-sm line-clamp-2">
                    {item.title}
                  </CardTitle>
                  <CardDescription className="text-xs flex items-center justify-between gap-4">
                    <span className="text-muted-foreground">
                      Created by {item.creator?.name}
                    </span>
                    <span className="text-muted-foreground">
                      Assigneed to {item.assignee?.name}
                    </span>
                  </CardDescription>
                </CardHeader>

                <CardContent className="grid gap-2">
                  <p className="text-sm line-clamp-3">{item.description}</p>

                  {item.report && (
                    <p className="text-sm line-clamp-3 text-muted-foreground">
                      Report: {item.report ?? "-"}
                    </p>
                  )}
                </CardContent>

                {statusId == 1 && user?.id == item.creator_id && (
                  <CardFooter className="border-t p-0 px-4 [.border-t]:pt-4 flex items-center gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="grow"
                      onClick={() => updateTask(item)}
                    >
                      Update Task
                    </Button>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="flex-none"
                        >
                          <MoreVertical className="h-3.5 w-3.5" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem
                          onSelect={() => updateStatus(item, 2)}
                        >
                          Set to Doing
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </CardFooter>
                )}

                {statusId == 2 && user?.id == item.creator_id && (
                  <CardFooter className="border-t p-0 px-4 [.border-t]:pt-4 flex gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="grow"
                      onClick={() => updateStatus(item, 3)}
                    >
                      Set to Done
                    </Button>

                    <Button
                      variant="destructive"
                      size="sm"
                      className="grow"
                      onClick={() => updateStatus(item, 4)}
                    >
                      Set to Cancel
                    </Button>
                  </CardFooter>
                )}

                {statusId == 3 && user?.id == item.creator_id && (
                  <CardFooter className="border-t p-0 px-4 [.border-t]:pt-4">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="w-full">
                          Actions
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-full">
                        <DropdownMenuItem onSelect={() => updateReport(item)}>
                          Update Report
                        </DropdownMenuItem>
                        {item.report == undefined && (
                          <DropdownMenuItem
                            onSelect={() => updateStatus(item, 1)}
                          >
                            Set to do
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </CardFooter>
                )}

                {statusId == 4 && user?.id == item.creator_id && (
                  <CardFooter className="border-t p-0 px-4 [.border-t]:pt-4 flex items-center gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="grow"
                      onClick={() => updateTask(item)}
                    >
                      Update Task
                    </Button>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="flex-none"
                        >
                          <MoreVertical className="h-3.5 w-3.5" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem
                          onSelect={() => updateStatus(item, 1)}
                        >
                          Set to do
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </CardFooter>
                )}
              </Card>
            ))
          ) : (
            <p className="text-muted-foreground text-sm italic text-center">
              No data
            </p>
          )}
        </CardContent>
      </Card>
    </>
  );
}
