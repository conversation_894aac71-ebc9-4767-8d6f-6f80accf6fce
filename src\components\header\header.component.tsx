import { Link } from "react-router";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
} from "../ui/breadcrumb";
import { Separator } from "../ui/separator";
import { SidebarTrigger } from "../ui/sidebar";
import { ChevronRight } from "lucide-react";

interface IProps {
  breadcrumbs: {
    title: string;
    url?: string;
  }[];
}

const HeaderComponent = ({ breadcrumbs }: IProps) => {
  return (
    <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mr-2 data-[orientation=vertical]:h-4"
        />
        <Breadcrumb>
          <BreadcrumbList>
            {breadcrumbs.map((v, k) => (
              <BreadcrumbItem key={`${v.title}-${k}`}>
                {k > 0 && <ChevronRight className="hidden md:block size-3.5" />}
                {v.url ? (
                  <BreadcrumbLink asChild>
                    <Link to={v.url}>{v.title}</Link>
                  </BreadcrumbLink>
                ) : (
                  <BreadcrumbPage>{v.title}</BreadcrumbPage>
                )}
              </BreadcrumbItem>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      </div>
    </header>
  );
};

export default HeaderComponent;
