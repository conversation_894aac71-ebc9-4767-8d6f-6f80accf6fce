import PaginationType, {
  Link as PaginationLinkType,
} from "@/types/pagination.type";
import { FC, useEffect, useState } from "react";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
} from "../ui/pagination";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Button } from "../ui/button";

const pageSizeLists: number[] = [10, 20, 50, 100];

interface IProps {
  data: PaginationType;
  currentPage: number;
  toPage: number;
  show: number;
  disabledPrev: boolean;
  disabledNext: boolean;
  onShowChanged: (value: number) => void;
  onPageChanged: (value: number) => void;
  onPrev: () => void;
  onNext: () => void;
}

const FooterDatatableComponent: FC<IProps> = ({
  currentPage,
  data,
  toPage,
  show,
  disabledPrev,
  disabledNext,
  onShowChanged,
  onPageChanged,
  onPrev,
  onNext,
}) => {
  const [links, setLinks] = useState<PaginationLinkType[]>([]);

  useEffect(() => {
    setLinks(
      data.links.filter(
        (item) => !["&laquo; Previous", "Next &raquo;"].includes(item.label)
      )
    );
  }, [data]);

  return (
    <div className="flex items-center justify-end space-x-2 py-2">
      <div className="flex-1 text-sm text-muted-foreground">
        Page {currentPage} of {toPage.toLocaleString()}
      </div>
      <div className="space-x-2 flex items-center">
        <Button
          variant="outline"
          size="sm"
          onClick={onPrev}
          disabled={disabledPrev}
        >
          Previous
        </Button>
        <Pagination>
          <PaginationContent>
            {links.map((link, key) => (
              <PaginationItem key={`links-${link.label}-${key}`}>
                {/^\d+$/.test(link.label) && (
                  <PaginationLink
                    size="sm"
                    isActive={link.active}
                    onClick={
                      link.active
                        ? undefined
                        : () => onPageChanged(Number(link.label) - 1)
                    }
                  >
                    {link.label}
                  </PaginationLink>
                )}

                {link.label === "..." && <PaginationEllipsis />}
              </PaginationItem>
            ))}
          </PaginationContent>
        </Pagination>
        <Button
          variant="outline"
          size="sm"
          onClick={onNext}
          disabled={disabledNext}
        >
          Next
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Show: {show}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {pageSizeLists.map((size) => (
              <DropdownMenuItem
                disabled={show == size}
                key={`size-${size}`}
                onClick={() => onShowChanged(size)}
              >
                {size}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default FooterDatatableComponent;
