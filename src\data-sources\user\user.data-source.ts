import { USER, USER_LIST } from "@/constants/api";
import { networkWithAuth } from "@/lib/network";
import UserModel from "@/models/user.model";
import PaginationType from "@/types/pagination.type";
import { ResponseAPI } from "@/types/response-api.type";

export async function getUserLists({
  per_page,
  page,
}: {
  per_page: number;
  page: number;
}): Promise<PaginationType<UserModel> | undefined> {
  try {
    const params: any = {
      per_page: per_page,
      page: page,
    };

    const { data } = await networkWithAuth().get(USER, {
      params: params,
    });
    const response = data.data;

    return response as PaginationType<UserModel>;
  } catch (_) {
    return undefined;
  }
}

export async function getSelectUserLists(): Promise<UserModel[]> {
  try {
    const { data } = await networkWithAuth().get(USER_LIST);
    const response = data.data;

    return response as UserModel[];
  } catch (_) {
    return [];
  }
}

export async function createUser({
  name,
  email,
  password,
  role_id,
}: {
  name: string;
  email: string;
  password: string;
  role_id: number;
}): Promise<ResponseAPI<undefined>> {
  try {
    const payload = {
      name: name,
      email: email,
      password: password,
      role_id: role_id,
    };

    const { data } = await networkWithAuth().post(USER, payload);

    return {
      success: true,
      message: data.message,
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message ?? "Terjadi Kesalahan",
    };
  }
}
