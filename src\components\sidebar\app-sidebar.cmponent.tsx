import {
  Home,
  Users2,
} from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON>ontent,
  <PERSON>bar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "../ui/sidebar";
import { NavUser } from "./nav-user.component";
import { Link, useLocation } from "react-router";
import { useMemo } from "react";
import { NavGroupComponent } from "./nav-group.component";

export function AppSidebarComponent({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const location = useLocation();

  const paths = useMemo(
    () => location.pathname.split("/").filter((_, k) => k != 0),
    [location]
  );

  const data = useMemo(() => {
    return {
      navMain: [
        {
          title: "Dashboard",
          url: "/",
          icon: Home,
          isActive: paths[0] == "",
        },
        {
          title: "User",
          url: "/user",
          icon: Users2,
          isActive: paths[0] == "user",
        },
      ],
    };
  }, [paths]);

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link to="/">
                <div className="flex flex-col gap-0.5 leading-none">
                  <span className="font-medium">IKT</span>
                  <span className="">v1.0.0</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavGroupComponent items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
