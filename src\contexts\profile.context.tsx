import { doLogout } from "@/data-sources/auth/auth.data-source";
import { getProfile } from "@/data-sources/profile/profile.data-source";
import UserModel from "@/models/user.model";
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";

export type ProfileContextState = {
  user?: UserModel;
  state: "INITIALIZE" | "AUTHENTICATED" | "UNAUTHENTICATED";
  getProfile: () => Promise<void>;
  logoutProfile: () => Promise<void>;
};
const initialValues: ProfileContextState = {
  user: undefined,
  state: "INITIALIZE",
  getProfile: async () => {},
  logoutProfile: async () => {},
};
const ProfileContext = createContext<ProfileContextState>(initialValues);

export const useProfileContext = () => useContext(ProfileContext);

export const ProfileContextProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [user, setUser] = useState<UserModel | undefined>(initialValues.user);
  const [stateUser, setStateUser] = useState<
    "INITIALIZE" | "AUTHENTICATED" | "UNAUTHENTICATED"
  >("INITIALIZE");

  const loadProfile = async () => {
    const user = await getProfile();
    setUser(user);
    setStateUser(user != undefined ? "AUTHENTICATED" : "UNAUTHENTICATED");
  };

  const logoutProfile = async () => {
    const { success } = await doLogout();

    if (success) {
      setUser(undefined);
      setStateUser("UNAUTHENTICATED");
    }
  };

  useEffect(() => {
    loadProfile();
  }, []);

  return (
    <ProfileContext.Provider
      value={{ user, getProfile: loadProfile, state: stateUser, logoutProfile }}
    >
      {children}
    </ProfileContext.Provider>
  );
};
