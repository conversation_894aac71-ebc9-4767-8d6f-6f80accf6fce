import { AppSidebarComponent } from "@/components/sidebar/app-sidebar.cmponent";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useProfileContext } from "@/contexts/profile.context";
import { useEffect } from "react";
import { Outlet, useNavigate } from "react-router";

const DashboardLayout = () => {
  const { user, state } = useProfileContext();
  const navigate = useNavigate();

  useEffect(() => {
    if (state == "UNAUTHENTICATED" && user == undefined) {
      navigate("/login", { replace: true });
    }
  }, [user, state]);

  return (
    <SidebarProvider>
      <AppSidebarComponent />
      <SidebarInset className="p-4 overflow-x-auto">
        <Outlet />
      </SidebarInset>
    </SidebarProvider>
  );
};

export default DashboardLayout;
