import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>lose,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useFormik } from "formik";
import { PlusCircle } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import * as yup from "yup";
import { createTask } from "@/data-sources/task/task.data-source";
import { useProfileContext } from "@/contexts/profile.context";
import { Textarea } from "@/components/ui/textarea";
import UserSelect from "@/components/select/user.select";

interface IProps {
  onSuccess: () => void;
}

interface formValues {
  title: string;
  description: string;
  assignee_id: number;
}

const initialValues: formValues = {
  title: "",
  description: "",
  assignee_id: 0,
};

export default function CreateTaskDialog({ onSuccess }: IProps) {
  const [loading, setLoading] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const { user } = useProfileContext();

  const handleSubmit = async (values: formValues) => {
    setLoading(true);
    const { success, message } = await createTask({
      title: values.title,
      description: values.description,
      assignee_id: values.assignee_id,
    });
    setLoading(false);

    if (success) {
      setOpen(false);
      onSuccess();
      toast.success(message);
    } else {
      toast.error(message);
    }
  };

  const handleOpenChanged = (_: boolean) => {
    if (loading) return;

    setOpen(_);
  };

  const formik = useFormik({
    initialValues: initialValues,
    onSubmit: handleSubmit,
    validationSchema: yup.object({
      title: yup.string().required("Title is required!"),
      description: yup.string().required("Description is required!"),
      assignee_id: yup.number().min(1, "Assignee is required!"),
    }),
  });

  const disabledButton = useMemo(
    () =>
      formik.values.title == "" ||
      formik.values.description == "" ||
      formik.values.assignee_id == 0,
    [formik]
  );

  useEffect(() => {
    if (!open) {
      formik.resetForm();
      formik.setValues({ ...initialValues });
    } else {
      formik.setValues({ ...initialValues, assignee_id: user?.id ?? 0 });
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={handleOpenChanged}>
      <DialogTrigger asChild>
        <Button size="sm" className="h-7 gap-1" type="button" variant="outline">
          <PlusCircle className="h-3.5 w-3.5" />
          <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
            Add Task
          </span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] mx-6 max-h-[80dvh] overflow-y-auto">
        <form onSubmit={formik.handleSubmit} className="grid gap-4">
          <DialogHeader>
            <DialogTitle>Create Task</DialogTitle>
            <DialogDescription>
              Make sure to fill the form correctly.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4">
            <div className="grid gap-3">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                required
                autoComplete="off"
                value={formik.values.title}
                onChange={(e) => {
                  formik.values.title = e.target.value;
                  formik.validateField("title");
                }}
              />
              <label
                htmlFor="error"
                className={`text-[12px] text-red-500 ${
                  formik.errors.title ? "" : "hidden"
                }`}
              >
                {formik.errors.title ? formik.errors.title : undefined}
              </label>
            </div>

            <div className="grid gap-3">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                required
                rows={5}
                autoComplete="off"
                value={formik.values.description}
                onChange={(e) => {
                  formik.values.description = e.target.value;
                  formik.validateField("description");
                }}
              />
              <label
                htmlFor="error"
                className={`text-[12px] text-red-500 ${
                  formik.errors.description ? "" : "hidden"
                }`}
              >
                {formik.errors.description
                  ? formik.errors.description
                  : undefined}
              </label>
            </div>

            {user?.role_id == 1 && (
              <div className="grid gap-3">
                <Label htmlFor="assignee_id">Assignee</Label>
                <UserSelect
                  value={formik.values.assignee_id.toString()}
                  onChange={(value) => {
                    formik.values.assignee_id = value?.id ?? 0;
                    formik.validateField("assignee_id");
                  }}
                />
                <label
                  htmlFor="error"
                  className={`text-[12px] text-red-500 ${
                    formik.errors.assignee_id ? "" : "hidden"
                  }`}
                >
                  {formik.errors.assignee_id
                    ? formik.errors.assignee_id
                    : undefined}
                </label>
              </div>
            )}
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button type="submit" disabled={disabledButton} loading={loading}>
              Save changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
