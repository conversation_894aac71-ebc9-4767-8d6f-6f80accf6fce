import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import tailwindcss from "@tailwindcss/vite";
import path from "path";

// https://vite.dev/config/
export default defineConfig(({ mode, isSsrBuild }) => {
  const env = loadEnv(mode || "test", process.cwd(), "");

  return {
    plugins: [
      react(),
      tailwindcss(),
    ],
    environments: {},
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    preview: {
      port: 3001,
      strictPort: true,
      allowedHosts: [
        "localhost",
        "0.0.0.0",
      ],
    },
    build: {
      rollupOptions: !isSsrBuild
        ? {
            output: {
              manualChunks: {
                react: ["react", "react-dom", "react-router"],
                lodash: ["lodash"],
              },
            },
          }
        : {},
    },
    server: {
      port: 3001,
      strictPort: true,
      host: true,
      proxy: {
        "/api": {
          target: `${env.VITE_BE_SCHEME}://${env.VITE_BE_ORIGIN}/api`,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ""),
        },
      },
    },
  };
});
