import HeaderComponent from "@/components/header/header.component";
import CardTaskComponent from "./components/card-task.component";
import { TaskContextProvider } from "@/contexts/task.context";

export default function HomePage() {
  return (
    <>
      <HeaderComponent breadcrumbs={[{ title: "Home" }]} />

      <TaskContextProvider>
        <main className="flex gap-4 w-full">
          <CardTaskComponent title="To Do" statusId={1} />
          <CardTaskComponent title="Doing" statusId={2} />
          <CardTaskComponent title="Done" statusId={3} />
          <CardTaskComponent title="Canceled" statusId={4} />
        </main>
      </TaskContextProvider>
    </>
  );
}
