import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useFormik } from "formik";
import { PlusCircle } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import * as yup from "yup";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { createUser } from "@/data-sources/user/user.data-source";

interface IProps {
  onSuccess: () => void;
}

interface formValues {
  name: string;
  email: string;
  password: string;
  role_id: number;
}

const initialValues: formValues = {
  name: "",
  email: "",
  password: "",
  role_id: 2,
};

export default function CreateUserDialog({ onSuccess }: IProps) {
  const [loading, setLoading] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);

  const handleSubmit = async (values: formValues) => {
    setLoading(true);
    const { success, message } = await createUser({
      name: values.name,
      email: values.email,
      password: values.password,
      role_id: values.role_id,
    });
    setLoading(false);

    if (success) {
      setOpen(false);
      onSuccess();
      toast.success(message);
    } else {
      toast.error(message);
    }
  };

  const handleOpenChanged = (_: boolean) => {
    if (loading) return;

    setOpen(_);
  };

  const formik = useFormik({
    initialValues: initialValues,
    onSubmit: handleSubmit,
    validationSchema: yup.object({
      name: yup.string().required("Name is required!"),
      email: yup.string().required("E-mail is required!"),
      password: yup.string().required("Password is required!"),
      role_id: yup.number().min(1, "Role is required!"),
    }),
  });

  const disabledButton = useMemo(
    () =>
      formik.values.name == "" ||
      formik.values.email == "" ||
      formik.values.password == "" ||
      formik.values.role_id == 0,
    [formik]
  );

  useEffect(() => {
    if (!open) {
      formik.resetForm();
      formik.setValues({ ...initialValues });
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={handleOpenChanged}>
      <DialogTrigger asChild>
        <Button size="sm" className="h-7 gap-1" type="button" variant="outline">
          <PlusCircle className="h-3.5 w-3.5" />
          <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
            Add User
          </span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] mx-6 max-h-[80dvh] overflow-y-auto">
        <form onSubmit={formik.handleSubmit} className="grid gap-4">
          <DialogHeader>
            <DialogTitle>Create User</DialogTitle>
            <DialogDescription>
              Make sure to fill the form correctly.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4">
            <div className="grid gap-3">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                name="name"
                required
                autoComplete="off"
                value={formik.values.name}
                onChange={(e) => {
                  formik.values.name = e.target.value;
                  formik.validateField("name");
                }}
              />
              <label
                htmlFor="error"
                className={`text-[12px] text-red-500 ${
                  formik.errors.name ? "" : "hidden"
                }`}
              >
                {formik.errors.name ? formik.errors.name : undefined}
              </label>
            </div>
            <div className="grid gap-3">
              <Label htmlFor="email">E-mail</Label>
              <Input
                id="email"
                name="email"
                required
                autoComplete="off"
                value={formik.values.email}
                onChange={(e) => {
                  formik.values.email = e.target.value;
                  formik.validateField("email");
                }}
              />
              <label
                htmlFor="error"
                className={`text-[12px] text-red-500 ${
                  formik.errors.email ? "" : "hidden"
                }`}
              >
                {formik.errors.email ? formik.errors.email : undefined}
              </label>
            </div>
            <div className="grid gap-3">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                name="password"
                type="password"
                required
                autoComplete="off"
                value={formik.values.password}
                onChange={(e) => {
                  formik.values.password = e.target.value;
                  formik.validateField("password");
                }}
              />
              <label
                htmlFor="error"
                className={`text-[12px] text-red-500 ${
                  formik.errors.password ? "" : "hidden"
                }`}
              >
                {formik.errors.password ? formik.errors.password : undefined}
              </label>
            </div>
            <div className="grid gap-3">
              <Label htmlFor="role_id">Role</Label>
              <Select
                name="role_id"
                required
                value={formik.values.role_id.toString()}
                onValueChange={(value) => {
                  formik.values.role_id = Number(value);
                  formik.validateField("role_id");
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="1">Manager</SelectItem>
                    <SelectItem value="2">Staff</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
              <label
                htmlFor="error"
                className={`text-[12px] text-red-500 ${
                  formik.errors.name ? "" : "hidden"
                }`}
              >
                {formik.errors.name ? formik.errors.name : undefined}
              </label>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button type="submit" disabled={disabledButton} loading={loading}>
              Save changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
